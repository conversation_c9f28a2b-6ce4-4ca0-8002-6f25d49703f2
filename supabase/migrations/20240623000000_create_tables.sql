-- 创建用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT NOT NULL UNIQUE,
  nickname TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone_number TEXT DEFAULT '',
  avatar_url TEXT DEFAULT '',
  bio TEXT DEFAULT '',
  following UUID[] DEFAULT ARRAY[]::UUID[],
  followers UUID[] DEFAULT ARRAY[]::UUID[],
  published_spots UUID[] DEFAULT ARRAY[]::UUID[],
  favorite_spots UUID[] DEFAULT ARRAY[]::UUID[],
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_login_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建钓点表
CREATE TABLE fishing_spots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location GEOGRAPHY(POINT, 4326) NOT NULL,
  name TEXT NOT NULL,
  shared_by UUID NOT NULL REFERENCES users(id),
  likes INTEGER NOT NULL DEFAULT 0,
  unlikes INTEGER NOT NULL DEFAULT 0,
  photo_urls TEXT[] DEFAULT ARRAY[]::TEXT[],
  panorama_photo_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  description TEXT DEFAULT ''
);

-- 创建消息表
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID NOT NULL REFERENCES users(id),
  receiver_id UUID NOT NULL REFERENCES users(id),
  content TEXT NOT NULL,
  sent_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_read BOOLEAN NOT NULL DEFAULT FALSE
);

