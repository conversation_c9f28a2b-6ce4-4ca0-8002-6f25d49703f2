import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';
import '../../main.dart';
import '../../services/enhanced_auth_service.dart';
import '../../services/user_service.dart';
import '../../services/fishing_spot_service.dart';
import '../../models/user.dart' as model_user;
import '../../models/fishing_spot.dart';

/// Supabase功能测试页面
/// 仅在开发模式下可用，用于测试各种Supabase后端功能
class SupabaseTestPage extends StatefulWidget {
  const SupabaseTestPage({super.key});

  @override
  State<SupabaseTestPage> createState() => _SupabaseTestPageState();
}

class _SupabaseTestPageState extends State<SupabaseTestPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 服务实例
  final EnhancedAuthService _authService = EnhancedAuthService();
  final UserService _userService = UserService();
  final FishingSpotService _spotService = FishingSpotService();

  // 测试数据
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _passwordCheckController =
      TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController();

  // 状态变量
  bool _isLoading = false;
  String _statusMessage = '';
  List<model_user.User> _users = [];
  List<FishingSpot> _spots = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _usernameController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    setState(() {
      _statusMessage = '正在加载初始数据...';
    });

    try {
      // 加载用户列表
      final users = await _userService.getAllUsers();
      final spots = await _spotService.getAllSpots();

      setState(() {
        _users = users;
        _spots = spots;
        _statusMessage = '数据加载完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '加载数据失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase 功能测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: '认证测试'),
            Tab(text: '用户管理'),
            Tab(text: '钓点管理'),
            Tab(text: '实时订阅'),
            Tab(text: '系统信息'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAuthTestTab(),
          _buildUserManagementTab(),
          _buildSpotManagementTab(),
          _buildRealtimeTab(),
          _buildSystemInfoTab(),
        ],
      ),
    );
  }

  /// 构建认证测试标签页
  Widget _buildAuthTestTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前认证状态',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('用户ID: ${supabase.auth.currentUser?.id ?? '未登录'}'),
                  Text('邮箱: ${supabase.auth.currentUser?.email ?? '无'}'),
                  Text('登录状态: ${_userService.isLoggedIn() ? '已登录' : '未登录'}'),
                  Text('当前用户: ${_userService.currentUser?.username ?? '无'}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 邮箱登录测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '邮箱注册/登录测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: '邮箱',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _passwordCheckController,
                    decoration: const InputDecoration(
                      labelText: '确认密码',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testEmailRegister,
                          child: const Text('注册'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _testLogout,
                          child: const Text('登出'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 手机号登录测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '手机号登录测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: '手机号',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _testPhoneLogin,
                    child: const Text('手机号登录/注册'),
                  ),
                ],
              ),
            ),
          ),

          // 状态信息
          if (_statusMessage.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  _statusMessage,
                  style: TextStyle(
                    color:
                        _statusMessage.contains('失败') ||
                                _statusMessage.contains('错误')
                            ? Colors.red
                            : Colors.green,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建用户管理标签页
  Widget _buildUserManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '用户列表 (${_users.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _refreshUsers,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_users.isEmpty)
                    const Text('暂无用户数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _users.length,
                      itemBuilder: (context, index) {
                        final user = _users[index];
                        return ListTile(
                          leading: CircleAvatar(
                            child: Text(
                              user.username.substring(0, 1).toUpperCase(),
                            ),
                          ),
                          title: Text(user.username),
                          subtitle: Text('${user.nickname} - ${user.email}'),
                          trailing: Text(
                            '钓点: ${user.publishedSpots.length}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 创建测试用户
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '创建测试用户',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _nicknameController,
                    decoration: const InputDecoration(
                      labelText: '昵称',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _createTestUser,
                    child: const Text('创建测试用户'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建钓点管理标签页
  Widget _buildSpotManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '钓点列表 (${_spots.length})',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton(
                        onPressed: _refreshSpots,
                        child: const Text('刷新'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_spots.isEmpty)
                    const Text('暂无钓点数据')
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _spots.length,
                      itemBuilder: (context, index) {
                        final spot = _spots[index];
                        return ListTile(
                          leading: const Icon(Icons.location_on),
                          title: Text(spot.name),
                          subtitle: Text(
                            '${spot.location.latitude.toStringAsFixed(4)}, '
                            '${spot.location.longitude.toStringAsFixed(4)}',
                          ),
                          trailing: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text('照片: ${spot.photoUrls.length}'),
                              Text('评论: ${spot.comments.length}'),
                            ],
                          ),
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 数据库操作测试
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '数据库操作测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: _testDatabaseRead,
                        child: const Text('读取测试'),
                      ),
                      ElevatedButton(
                        onPressed: _testDatabaseWrite,
                        child: const Text('写入测试'),
                      ),
                      ElevatedButton(
                        onPressed: _testDatabaseUpdate,
                        child: const Text('更新测试'),
                      ),
                      ElevatedButton(
                        onPressed: _testDatabaseDelete,
                        child: const Text('删除测试'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建实时订阅标签页
  Widget _buildRealtimeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '实时订阅测试',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  const Text('实时订阅功能可以监听数据库变化，实现实时更新。'),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: _testRealtimeSubscription,
                        child: const Text('测试实时订阅'),
                      ),
                      ElevatedButton(
                        onPressed: _stopRealtimeSubscription,
                        child: const Text('停止订阅'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建系统信息标签页
  Widget _buildSystemInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Supabase 连接信息',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  Text('URL: http://*************/'),
                  Text('匿名密钥: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'),
                  Text(
                    '当前会话: ${supabase.auth.currentSession?.accessToken.substring(0, 20) ?? '无'}...',
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('数据统计', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  Text('用户总数: ${_users.length}'),
                  Text('钓点总数: ${_spots.length}'),
                  Text('当前登录用户: ${_userService.currentUser?.username ?? '无'}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('测试操作', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: _testConnection,
                        child: const Text('测试连接'),
                      ),
                      ElevatedButton(
                        onPressed: _clearCache,
                        child: const Text('清除缓存'),
                      ),
                      ElevatedButton(
                        onPressed: _loadInitialData,
                        child: const Text('重新加载数据'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // ===== 测试方法实现 =====

  /// 测试邮箱注册
  Future<void> _testEmailRegister() async {
    if (_emailController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _passwordCheckController.text.isEmpty) {
      _updateStatus('请输入邮箱、密码和确认密码');
      return;
    }

    if (_passwordController.text != _passwordCheckController.text) {
      _updateStatus('密码和确认密码不匹配');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在注册...';
    });

    try {
      final response = await supabase.auth.signUp(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (response.user != null) {
        _updateStatus('注册成功: ${_emailController.text}');
        debugPrint('注册成功，用户ID: ${response.user!.id}');

        // 注册成功后自动登录
        await _testEmailLogin();
        await _loadInitialData();
      } else {
        _updateStatus('注册失败');
      }
    } catch (e) {
      _updateStatus('注册失败: $e');
      debugPrint('注册失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试邮箱登录
  Future<void> _testEmailLogin() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      _updateStatus('请输入邮箱和密码');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在登录...';
    });

    try {
      final user = await _authService.login(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (user != null) {
        _updateStatus('登录成功: ${user.username}');
        await _loadInitialData();
      } else {
        _updateStatus('登录失败');
      }
    } catch (e) {
      _updateStatus('登录失败: $e');
      debugPrint('登录失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试手机号登录
  Future<void> _testPhoneLogin() async {
    if (_phoneController.text.isEmpty) {
      _updateStatus('请输入手机号');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在登录...';
    });

    try {
      final user = await _authService.phoneLogin(
        phoneNumber: _phoneController.text,
      );

      if (user != null) {
        _updateStatus('手机号登录成功: ${user.username}');
        await _loadInitialData();
      } else {
        _updateStatus('手机号登录失败');
      }
    } catch (e) {
      _updateStatus('手机号登录失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试登出
  Future<void> _testLogout() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在登出...';
    });

    try {
      await _authService.logout();
      _updateStatus('登出成功');
      await _loadInitialData();
    } catch (e) {
      _updateStatus('登出失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 创建测试用户
  Future<void> _createTestUser() async {
    if (_usernameController.text.isEmpty || _nicknameController.text.isEmpty) {
      _updateStatus('请输入用户名和昵称');
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在创建测试用户...';
    });

    try {
      final testUser = model_user.User(
        id: const Uuid().v4(),
        username: _usernameController.text,
        nickname: _nicknameController.text,
        email: '${_usernameController.text}@test.com',
        phoneNumber: '13800138000',
        bio: '这是一个测试用户',
        avatarUrl: '',
        following: [],
        followers: [],
        publishedSpots: [],
        favoriteSpots: [],
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      await _userService.createUserInDatabase(testUser);
      _updateStatus('测试用户创建成功: ${testUser.username}');
      await _refreshUsers();
    } catch (e) {
      _updateStatus('创建测试用户失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 刷新用户列表
  Future<void> _refreshUsers() async {
    setState(() {
      _statusMessage = '正在刷新用户列表...';
    });

    try {
      final users = await _userService.getAllUsers();
      setState(() {
        _users = users;
        _statusMessage = '用户列表刷新完成';
      });
    } catch (e) {
      _updateStatus('刷新用户列表失败: $e');
    }
  }

  /// 刷新钓点列表
  Future<void> _refreshSpots() async {
    setState(() {
      _statusMessage = '正在刷新钓点列表...';
    });

    try {
      final spots = await _spotService.getAllSpots();
      setState(() {
        _spots = spots;
        _statusMessage = '钓点列表刷新完成';
      });
    } catch (e) {
      _updateStatus('刷新钓点列表失败: $e');
    }
  }

  /// 测试数据库读取
  Future<void> _testDatabaseRead() async {
    setState(() {
      _statusMessage = '正在测试数据库读取...';
    });

    try {
      final response = await supabase.from('users').select().limit(5);
      _updateStatus('数据库读取成功，获取到 ${response.length} 条记录');
    } catch (e) {
      _updateStatus('数据库读取失败: $e');
    }
  }

  /// 测试数据库写入
  Future<void> _testDatabaseWrite() async {
    if (!_userService.isLoggedIn()) {
      _updateStatus('请先登录后再测试写入功能');
      return;
    }

    setState(() {
      _statusMessage = '正在测试数据库写入...';
    });

    try {
      // 创建一个测试钓点
      final testSpot = FishingSpot(
        id: const Uuid().v4(),
        name: '测试钓点_${DateTime.now().millisecondsSinceEpoch}',
        location: const LatLng(39.9042, 116.4074), // 北京坐标
        description: '这是一个测试钓点',
        sharedBy: _userService.currentUser!.username,
        createdAt: DateTime.now(),
      );

      final result = await _spotService.addSpot(testSpot);
      if (result != null) {
        _updateStatus('数据库写入成功，创建了测试钓点');
        await _refreshSpots();
      } else {
        _updateStatus('数据库写入失败');
      }
    } catch (e) {
      _updateStatus('数据库写入失败: $e');
    }
  }

  /// 测试数据库更新
  Future<void> _testDatabaseUpdate() async {
    if (!_userService.isLoggedIn()) {
      _updateStatus('请先登录后再测试更新功能');
      return;
    }

    setState(() {
      _statusMessage = '正在测试数据库更新...';
    });

    try {
      final currentUser = _userService.currentUser!;
      final updatedUser = model_user.User(
        id: currentUser.id,
        username: currentUser.username,
        nickname: '${currentUser.nickname}_updated',
        email: currentUser.email,
        phoneNumber: currentUser.phoneNumber,
        bio: '${currentUser.bio} - 测试更新',
        avatarUrl: currentUser.avatarUrl,
        following: currentUser.following,
        followers: currentUser.followers,
        publishedSpots: currentUser.publishedSpots,
        favoriteSpots: currentUser.favoriteSpots,
        createdAt: currentUser.createdAt,
        lastLoginAt: DateTime.now(),
      );

      await _userService.updateUser(updatedUser);
      _updateStatus('数据库更新成功');
      await _refreshUsers();
    } catch (e) {
      _updateStatus('数据库更新失败: $e');
    }
  }

  /// 测试数据库删除
  Future<void> _testDatabaseDelete() async {
    setState(() {
      _statusMessage = '数据库删除测试需要谨慎实现，避免误删重要数据';
    });
  }

  /// 测试实时订阅
  Future<void> _testRealtimeSubscription() async {
    setState(() {
      _statusMessage = '实时订阅功能测试 - 监听用户表变化';
    });

    try {
      // 这里可以添加实时订阅的测试代码
      _updateStatus('实时订阅功能需要进一步实现');
    } catch (e) {
      _updateStatus('实时订阅测试失败: $e');
    }
  }

  /// 停止实时订阅
  Future<void> _stopRealtimeSubscription() async {
    setState(() {
      _statusMessage = '已停止实时订阅';
    });
  }

  /// 测试连接
  Future<void> _testConnection() async {
    setState(() {
      _statusMessage = '正在测试Supabase连接...';
    });

    try {
      await supabase.from('users').select().limit(1);
      _updateStatus('连接测试成功，数据库响应正常');
    } catch (e) {
      _updateStatus('连接测试失败: $e');
    }
  }

  /// 清除缓存
  Future<void> _clearCache() async {
    setState(() {
      _statusMessage = '正在清除缓存...';
      _users.clear();
      _spots.clear();
    });

    _updateStatus('缓存已清除');
  }

  /// 更新状态信息
  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });

    // 显示snackbar
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}
